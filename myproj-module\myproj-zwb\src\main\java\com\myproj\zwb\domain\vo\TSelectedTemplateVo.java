package com.myproj.zwb.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.myproj.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;



/**
 * 已选病历模板(中间)视图对象 t_selected_template
 *
 * <AUTHOR>
 * @date 2021-10-25
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("已选病历模板(中间)视图对象")
public class TSelectedTemplateVo {

	private static final long serialVersionUID = 1L;

	/** id */
	@ApiModelProperty("id")
	private String id;

	/** 就诊记录id */
	@Excel(name = "就诊记录id")
	@ApiModelProperty("就诊记录id")
	private String visitRecordId;

	/** 病历模板字典key */
	@Excel(name = "病历模板字典key")
	@ApiModelProperty("病历模板字典key")
	private String templateDictKey;

	/** 创建时间 */
	private Date createTime;

	/** 数据类型*/
	private Integer type;

	/**统一的时间字段,方便前端处理*/
	private Date time;

	/**小程序需要用到的回显题目*/
	private String title;


	/** 病历模板id */
	private String templateId;
	/** 排序 */
	private Long templateOrder;
	private String programmeType;

	private String templateType;

	private String fileData;

	private String wpId;
}
