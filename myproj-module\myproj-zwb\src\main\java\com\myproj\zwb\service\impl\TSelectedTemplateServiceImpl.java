package com.myproj.zwb.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.myproj.common.core.domain.AjaxResult;
import com.myproj.common.core.domain.entity.SysDictData;
import com.myproj.common.core.mybatisplus.core.ServicePlusImpl;
import com.myproj.common.core.page.PagePlus;
import com.myproj.common.core.page.TableDataInfo;
import com.myproj.common.exception.CustomException;
import com.myproj.common.utils.DictUtils;
import com.myproj.common.utils.PageUtils;
import com.myproj.common.utils.StringUtils;
import com.myproj.system.service.ISysDictDataService;
import com.myproj.zwb.domain.*;
import com.myproj.zwb.domain.bo.TSelectedTemplateAddBo;
import com.myproj.zwb.domain.bo.TSelectedTemplateEditBo;
import com.myproj.zwb.domain.bo.TSelectedTemplateQueryBo;
import com.myproj.zwb.domain.bo.WxMedicalQueryBo;
import com.myproj.zwb.domain.vo.TSelectedTemplateVo;
import com.myproj.zwb.mapper.TSelectedTemplateMapper;
import com.myproj.zwb.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 已选病历模板(中间)Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-25
 */
@Service
public class TSelectedTemplateServiceImpl extends ServicePlusImpl<TSelectedTemplateMapper, TSelectedTemplate> implements ITSelectedTemplateService {
	@Autowired
	private ITaskInfoService taskInfoService;
	@Autowired
	private ITVisitRecordService itVisitRecordService;
	@Autowired
	private IProgrammeTypeService programmeTypeService;
	@Autowired
	private IGPathStageInfoService pathStageInfoService;
	@Autowired
	private ISysDictDataService dictDataService;
	@Autowired
	private IFTemplateAnswerService ifTemplateAnswerService;
	@Autowired
	private INutritionInterventionService nutritionInterventionService;


	/**
	 * 查询单个
	 *
	 * @return
	 */
	@Override
	public TSelectedTemplateVo queryById(String id) {
		return getVoById(id, TSelectedTemplateVo.class);
	}

	/**
	 * 分页查询返回table列表
	 */
	@Override
	public TableDataInfo<TSelectedTemplateVo> queryPageList(TSelectedTemplateQueryBo bo) {
		PagePlus<TSelectedTemplate, TSelectedTemplateVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), TSelectedTemplateVo.class);
		result.getRecordsVo().stream().forEach(item->{
			if(item.getTemplateDictKey()!=null){
				SysDictData sysDictData = dictDataService.getOne(new QueryWrapper<SysDictData>().eq("dict_type","zwb_medical_template").eq("dict_value",item.getTemplateDictKey()));
				if(sysDictData!=null && "t_f_template_answer".equals(sysDictData.getRemark())){
					item.setTemplateType("1");
					FTemplateAnswer fTemplateAnswer = ifTemplateAnswerService.getOne(new QueryWrapper<FTemplateAnswer>().eq("patient_id", bo.getPatientId()).eq("visit_id", bo.getVisitRecordId()).eq("quest_id", item.getTemplateDictKey()).last("limit 1"));
					if(fTemplateAnswer!=null){
						item.setTemplateId(fTemplateAnswer.getId());
					}
				}else{
					item.setTemplateType("2");
				}
			}
		});
		return PageUtils.buildDataInfo(result);
	}

	/*
	 * 查询返回list列表
	 */
	@Override
	public List<TSelectedTemplateVo> queryList(TSelectedTemplateQueryBo bo) {
		return listVo(buildQueryWrapper(bo), TSelectedTemplateVo.class);
	}

	/**
	 * 处理查询条件
	 */
	private LambdaQueryWrapper<TSelectedTemplate> buildQueryWrapper(TSelectedTemplateQueryBo bo) {
		Map<String, Object> params = bo.getParams();
		LambdaQueryWrapper<TSelectedTemplate> lqw = Wrappers.lambdaQuery();
		lqw.eq(StrUtil.isNotBlank(bo.getVisitRecordId()), TSelectedTemplate::getVisitRecordId, bo.getVisitRecordId());
		lqw.eq(bo.getTemplateDictKey() != null, TSelectedTemplate::getTemplateDictKey, bo.getTemplateDictKey());
		lqw.last(" order by visit_record_id,template_order ");
		return lqw;
	}

	/**
	 * 根据新增业务对象插入已选病历模板(中间)
	 *
	 * @param bo 已选病历模板(中间)新增业务对象
	 * @return
	 */
	@Override
	public Boolean insertByAddBo(TSelectedTemplateAddBo bo) {
		TSelectedTemplate add = BeanUtil.toBean(bo, TSelectedTemplate.class);
		validEntityBeforeSave(add);
		return save(add);
	}

	/**
	 * 根据编辑业务对象修改已选病历模板(中间)
	 *
	 * @param bo 已选病历模板(中间)编辑业务对象
	 * @return
	 */
	@Override
	public Boolean updateByEditBo(TSelectedTemplateEditBo bo) {
		TSelectedTemplate update = BeanUtil.toBean(bo, TSelectedTemplate.class);
		validEntityBeforeSave(update);
		return updateById(update);
	}

	/**
	 * 保存前的数据校验
	 *
	 * @param entity 实体类数据
	 */
	private void validEntityBeforeSave(TSelectedTemplate entity) {
		//TODO 做一些数据校验,如唯一约束
	}

	/**
	 * 校验并删除数据
	 *
	 * @param ids     主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	@Override
	public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
		if (isValid) {
			//TODO 做一些业务上的校验,判断是否需要校验
		}
		return removeByIds(ids);
	}

	@Override
	public TableDataInfo<List<TSelectedTemplateVo>> queryPagedetailList(TSelectedTemplateQueryBo bo) {
		if (StrUtil.isBlank(bo.getPatientId()) && StrUtil.isBlank(bo.getVisitRecordId())) {
			return null;
		}

		PagePlus<TSelectedTemplate, TSelectedTemplateVo> result;
		if (StrUtil.isNotBlank(bo.getPatientId())) {
			List<TVisitRecord> list = itVisitRecordService.lambdaQuery().eq(TVisitRecord::getPatientId, bo.getPatientId()).eq(TVisitRecord::getVisitType, 1).list();
			LambdaQueryWrapper<TSelectedTemplate> selectedTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
			List<String> visitRecords = list.stream().map(item -> item.getId()).collect(Collectors.toList());
			if(visitRecords.size()>0) {
				selectedTemplateLambdaQueryWrapper.in(TSelectedTemplate::getVisitRecordId, visitRecords);
				result = pageVo(PageUtils.buildPagePlus(), selectedTemplateLambdaQueryWrapper, TSelectedTemplateVo.class);
			}else {
				LambdaQueryWrapper<TSelectedTemplate> lqw = buildQueryWrapper(bo);
				lqw.eq(TSelectedTemplate::getId,"0");
				result= pageVo(PageUtils.buildPagePlus(), lqw, TSelectedTemplateVo.class);
			}
		} else {
			LambdaQueryWrapper<TSelectedTemplate> lqw = buildQueryWrapper(bo);
			lqw.eq(TSelectedTemplate::getId,"0");
			result= pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo), TSelectedTemplateVo.class);

		}
		List<TSelectedTemplateVo> recordsVo = result.getRecordsVo();
		recordsVo.stream().forEach(item->{
			if(item.getTemplateDictKey()!=null){
				SysDictData sysDictData = dictDataService.getOne(new QueryWrapper<SysDictData>().eq("dict_type","zwb_medical_template").eq("dict_value",item.getTemplateDictKey()));
				if(sysDictData!=null && "t_f_template_answer".equals(sysDictData.getRemark())){
					item.setTemplateType("1");
					FTemplateAnswer fTemplateAnswer = ifTemplateAnswerService.getOne(new QueryWrapper<FTemplateAnswer>().eq("patient_id", bo.getPatientId()).eq("visit_id", bo.getVisitRecordId()).eq("quest_id", item.getTemplateDictKey()).last("limit 1"));
					if(fTemplateAnswer!=null){
						item.setTemplateId(fTemplateAnswer.getId());
					}
				}else{
					item.setTemplateType("2");
				}
			}
		});
		List<SysDictData> template = DictUtils.getDictCache("zwb_medical_template");
		Map<String, String> tem = template.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (k1, k2) -> k1));
		//设置检查标记
		for (TSelectedTemplateVo TSelectedTemplateVo : recordsVo) {
			if (tem.containsKey(TSelectedTemplateVo.getTemplateDictKey()))TSelectedTemplateVo.setTitle(tem.get(TSelectedTemplateVo.getTemplateDictKey()));
			TSelectedTemplateVo.setType(5);
			TSelectedTemplateVo.setTime(TSelectedTemplateVo.getCreateTime());
		}
		//纯map分组
		Map<Date, List<TSelectedTemplateVo>> sorMap = recordsVo.stream().collect(Collectors.groupingBy((p -> p.getCreateTime()), Collectors.toList()));
		//取key转list并且时间逆序排序
		Set<Date> dates = sorMap.keySet();
		ArrayList<Date> arrayList = new ArrayList(dates);
		List<Date> dateSortList = arrayList.stream().sorted(Comparator.comparing(Date::getTime).reversed()).collect(Collectors.toList());
		List<List<TSelectedTemplateVo>> sortList = new LinkedList<>();
		//遍历填充
		for (Date date : dateSortList) {
			sortList.add(sorMap.get(date));
		}
		//重构结果集
		PagePlus<TSelectedTemplate, List<TSelectedTemplateVo>> trueresult = new PagePlus<>();
		trueresult.setRecordsVo(sortList);
		trueresult.setTotal(result.getTotal());
		return PageUtils.buildDataInfo(trueresult);
	}


	/**
	 * 分页查询返回table列表
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<TSelectedTemplateVo> queryPageListFWX(TSelectedTemplateQueryBo bo) {
		// 取出就诊记录id
		final List<String> visitRecords = itVisitRecordService.lambdaQuery()
				.eq(TVisitRecord::getPatientId, bo.getPatientId())
				.eq(TVisitRecord::getVisitType, 1)
				.eq(TVisitRecord::getVisitStatus, 2)
				.like(StringUtils.isNotEmpty(bo.getVisitRecordId()), TVisitRecord::getId, bo.getVisitRecordId())
				.orderByDesc(TVisitRecord::getVisitDate)
				.list().parallelStream().filter(Objects::nonNull).map(TVisitRecord::getId)
				.filter(StrUtil::isNotEmpty).collect(Collectors.toList());
		// 查询健康建议信息
		List<TSelectedTemplateVo> templateList;
		if (CollUtil.isNotEmpty(visitRecords)) {
			templateList = list(Wrappers.lambdaQuery(TSelectedTemplate.class)
					.in(TSelectedTemplate::getVisitRecordId, visitRecords))
					.parallelStream().filter(Objects::nonNull)
					.map(item -> BeanUtil.toBean(item, TSelectedTemplateVo.class))
					.peek(item -> {
						final SysDictData medicalTemplate = DictUtils.getCacheDictObject("zwb_medical_template", String.valueOf(item.getTemplateDictKey()));
						if (Objects.nonNull(medicalTemplate)) {
							item.setTitle(medicalTemplate.getDictLabel());
						}
						if (item.getTemplateOrder() == 0) {
							item.setTemplateOrder(2L);
						}
						if (item.getTemplateOrder() == 1) {
							item.setTemplateOrder(3L);
						}
					})
					.filter(item -> {
						final SysDictData dictData = DictUtils.getCacheDictObject("zwb_medical_template", String.valueOf(item.getTemplateDictKey()));
						return !dictData.getIsShow().equals("N");
					})
					// 去重
					.collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
							new TreeSet<>(Comparator.comparing(TSelectedTemplateVo::getTemplateDictKey))), ArrayList::new));
		} else {
			templateList = ListUtil.toList();
		}

		// 检查患者是否存在 干预方案
		if (CollUtil.isNotEmpty(visitRecords) && taskInfoService.count(
			Wrappers.lambdaQuery(TaskInfo.class)
				.eq(TaskInfo::getPatientId, bo.getPatientId())
				.in(TaskInfo::getVisitRecordId, visitRecords)
				.isNotNull(TaskInfo::getProgrammeTypeId)
		) > 0) {
			List<String> programTypeIds = taskInfoService.getProgrammeTypeIdsByPatientId(bo.getPatientId());
			if (programTypeIds.size()>0) {
				for (String programmeTypeId : programTypeIds) {
					final ProgrammeType programmeType = programmeTypeService.getById(programmeTypeId);
//						final ProgrammeType programmeType = programmeTypeService.getById(info.getProgrammeTypeId());
					if (Objects.nonNull(programmeType)) {
						templateList.add(
							new TSelectedTemplateVo()
								.setTitle(
									programmeType.getName()
								)
								.setTemplateOrder(1L)
								.setTemplateDictKey("0")
								.setTemplateId(programmeTypeId)
								.setProgrammeType("1")
							/** 干预方案类型 1.干预方案 2.路径方案中的干预方案 */
						);
					} else {
						final GPathStageInfo stageInfo = pathStageInfoService.getById(programmeTypeId);
						templateList.add(
							new TSelectedTemplateVo()
								.setTitle(
									StrUtil.isNotEmpty(stageInfo.getStageName()) ? stageInfo.getStageName() : "未知方案"
								)
								.setTemplateOrder(1L)
								.setTemplateDictKey("0")
								.setCreateTime(stageInfo.getCreateTime())
								.setTemplateId(programmeTypeId)
								.setProgrammeType("2")
						);
					}
				}
			}
		}

		// 根据templateOrder排序
		return templateList.stream()
				.filter(Objects::nonNull)
				.sorted(
						Comparator.comparing(TSelectedTemplateVo::getTemplateOrder, Comparator.nullsLast(Long::compareTo))
				).collect(Collectors.toList());
	}

	/**
	 * 分页查询返回table列表
	 * v2版本：禅道任务：2176 医生给患者下达多条健康指导时，患者端也同步显示多条
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<TSelectedTemplateVo> queryPageListFWXV2(TSelectedTemplateQueryBo bo) {
		// 取出就诊记录id
		final List<String> visitRecords = itVisitRecordService.lambdaQuery()
			.eq(TVisitRecord::getPatientId, bo.getPatientId())
			.eq(TVisitRecord::getVisitType, 1)
			.eq(TVisitRecord::getVisitStatus, 2)
			.like(StringUtils.isNotEmpty(bo.getVisitRecordId()), TVisitRecord::getId, bo.getVisitRecordId())
			.orderByDesc(TVisitRecord::getVisitDate)
			.select(TVisitRecord::getId)
			.list().parallelStream().filter(Objects::nonNull).map(TVisitRecord::getId)
			.filter(StrUtil::isNotEmpty).collect(Collectors.toList());
		// 查询健康建议信息
		List<TSelectedTemplateVo> templateList;
		if (CollUtil.isNotEmpty(visitRecords)) {
			templateList = list(Wrappers.lambdaQuery(TSelectedTemplate.class)
				.in(TSelectedTemplate::getVisitRecordId, visitRecords)
				.orderByDesc(TSelectedTemplate::getCreateTime))
				.parallelStream().filter(Objects::nonNull)
				.map(item -> BeanUtil.toBean(item, TSelectedTemplateVo.class))
				.peek(item -> {
					final SysDictData medicalTemplate = DictUtils.getCacheDictObject("zwb_medical_template", String.valueOf(item.getTemplateDictKey()));
					if (Objects.nonNull(medicalTemplate)) {
						item.setTitle(medicalTemplate.getDictLabel());
					}
					if (item.getTemplateOrder() != null && item.getTemplateOrder() == 0) {
						item.setTemplateOrder(2L);
					}
					if (item.getTemplateOrder() != null && item.getTemplateOrder() == 1) {
						item.setTemplateOrder(3L);
					}
				})
				.filter(item -> {
					final SysDictData dictData = DictUtils.getCacheDictObject("zwb_medical_template", String.valueOf(item.getTemplateDictKey()));
					if (dictData != null) {
						return !dictData.getIsShow().equals("N");
					}
					return false;
				})
				.collect(Collectors.toList());

			// 获取最新一次就诊记录
			TVisitRecord lastVisitRecord = itVisitRecordService.lambdaQuery()
				.eq(TVisitRecord::getPatientId, bo.getPatientId())
				.eq(TVisitRecord::getVisitType, 1)
				.eq(TVisitRecord::getVisitStatus, 2)
				.isNotNull(TVisitRecord::getEndTime)
				.like(StringUtils.isNotEmpty(bo.getVisitRecordId()), TVisitRecord::getId, bo.getVisitRecordId())
				.orderByDesc(TVisitRecord::getEndTime)
				.last("limit 1")
				.one();

			// 检查患者是否存在 干预方案
			// programType 1.干预方案 2.路径方案中的干预方案 3.诊后计划 4.宣教方案
			if (taskInfoService.count(
				Wrappers.lambdaQuery(TaskInfo.class)
					.eq(TaskInfo::getPatientId, bo.getPatientId())
					.in(TaskInfo::getProgrammeType, new Object[]{1, 2})
					.le(TaskInfo::getCreateTime, lastVisitRecord.getEndTime())
					.isNotNull(TaskInfo::getProgrammeTypeId)
			) > 0) {
				// 补充 干预方案 信息
				List<TaskInfo> infoList = taskInfoService.list(
					Wrappers.lambdaQuery(TaskInfo.class)
						.eq(TaskInfo::getPatientId, bo.getPatientId())
						.isNotNull(TaskInfo::getProgrammeTypeId)
						.in(TaskInfo::getProgrammeType, new Object[]{1, 2})
						.le(TaskInfo::getCreateTime, lastVisitRecord.getEndTime())
						.orderByDesc(TaskInfo::getCreateTime)
				);
				// 根据方案id去重
				infoList.stream()
					.collect(Collectors.toMap(TaskInfo::getProgrammeTypeId, Function.identity(), (oldValue, newValue) -> oldValue))
					.values()
					.stream()
					.forEach(info -> {
						final ProgrammeType programmeType = programmeTypeService.getById(info.getProgrammeTypeId());
						if (Objects.nonNull(programmeType)) {
							templateList.add(
								new TSelectedTemplateVo()
									.setTitle(
										programmeType.getName()
									)
									.setTemplateOrder(1L)
									.setTemplateDictKey("0")
									.setCreateTime(info.getCreateTime())
									.setTemplateId(info.getProgrammeTypeId())
									.setProgrammeType(info.getProgrammeType())
							);
						} else {
							final GPathStageInfo stageInfo = pathStageInfoService.getById(info.getProgrammeTypeId());
							if (Objects.nonNull(stageInfo)) {
								templateList.add(
									new TSelectedTemplateVo()
										.setTitle(
											StrUtil.isNotEmpty(stageInfo.getStageName()) ? stageInfo.getStageName() : "未知方案"
										)
										.setTemplateOrder(1L)
										.setTemplateDictKey("0")
										.setCreateTime(stageInfo.getCreateTime())
										.setTemplateId(info.getProgrammeTypeId())
										.setProgrammeType(info.getProgrammeType())
								);
							}
						}
					});
			}
		} else {
			templateList = ListUtil.toList();
		}
		// 根据templateOrder排序
		return templateList.stream()
			.filter(Objects::nonNull)
			.sorted(Comparator.comparing(TSelectedTemplateVo::getCreateTime).reversed())
			.peek(item->{
				// pdf预览
				if ("36".equals(item.getTemplateDictKey())){
					NutritionIntervention nutritionIntervention = nutritionInterventionService.getOne(new QueryWrapper<NutritionIntervention>().eq("visit_id", item.getVisitRecordId()).eq("patient_id",bo.getPatientId()),false);
					if (nutritionIntervention != null) {
						item.setFileData(nutritionIntervention.getFileData());
						item.setWpId(nutritionIntervention.getId());
					}
				}
			})
			.collect(Collectors.toList());
	}


	@Override
	public Map queryMedicalInfo(WxMedicalQueryBo bo) {
		SysDictData medicalTemplate = DictUtils.getCacheDictObject("zwb_medical_template", bo.getDictValue());
		String tableName = medicalTemplate.getRemark();
		Map<String, Object> resultMap = baseMapper.queryMedicalInfo(bo, tableName);
		//后端将json格式化,方便前端使用
		if (resultMap == null) {
			throw new CustomException("报告不存在");
		}
		for (Map.Entry<String, Object> stringObjectEntry : resultMap.entrySet()) {
			Object value = stringObjectEntry.getValue();
			if (JSONUtil.isJson(String.valueOf(value))) {
				resultMap.put(stringObjectEntry.getKey(), JSONUtil.parseObj(value));
			}
		}
		return resultMap;
	}
}
